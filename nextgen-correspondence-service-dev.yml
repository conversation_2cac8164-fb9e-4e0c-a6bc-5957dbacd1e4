---
env: ${CORRO_ENV}
# Spring application configuration
spring:
  # Name of the Spring application
  application:
    name: nextgen-correspondence-service
  config:
    # Connect to Spring Cloud Config Server if available
    import: "optional:configserver:"
  kafka:
    consumer:
      properties:
        spring.json.trusted.packages: "*"
      auto-offset-reset: earliest
  # MongoDB database configuration
  data:
    mongodb:
      # MongoDB connection URI with
      # environment-specific host and credentials
      uri: mongodb+srv://${CORRO_SERVICE_MONGO_USER}:${CORRO_SERVICE_MONGO_PASSWORD}@correspondence-${env}-pl-0.qih1s.mongodb.net/?retryWrites=true&w=majority&appName=correspondence-${env}
      # Socket timeout in milliseconds for MongoDB operations
      socketTimeout: 150000
      # MongoDB database name
      database: ${CORRO_SERVICE_MONGODB_DATABASE}

# API Gateway Configuration
apiGateway:
  api:
    base:
      url: https://correspondence-${env}.zinnia.com/apigateway/api/v1
  riskClass:
    api:
      url: ${apiGateway.api.base.url}/riskclass/translate
  pomAgentData:
    api:
      url: ${apiGateway.api.base.url}/agent/details
  carrierData:
    api:
      url: ${apiGateway.api.base.url}/carrier/details
  transactionStore:
    api:
      url: ${apiGateway.api.base.url}/casestore/search
  partyReference:
    api:
      url: ${apiGateway.api.base.url}/preferences/partyreference
  ePreferences:
    api:
      url: ${apiGateway.api.base.url}/preferences/edelivery
    policyReceipt:
      docType: NWB
  eds:
    search:
      api:
        url: ${apiGateway.api.base.url}/eds/search
      documentClassification: INBOUND
      documentType: POLPGFORM
    download:
      api:
        url: ${apiGateway.api.base.url}/eds/download
  zahara:
    policyDetails:
      inputVersion:
        api:
          url: ${apiGateway.api.base.url}/zahara/policy/version
      latestVersion:
        api:
          url: ${apiGateway.api.base.url}/zahara/policy/latest
    transactionDetails:
      api:
        url: ${apiGateway.api.base.url}/zahara/transaction
    planCode:
      api:
        url: ${apiGateway.api.base.url}/zahara/policy/planCode
  awsUpload:
    api:
      url: ${apiGateway.api.base.url}/aws/upload
  ruleEngine:
    base:
      api:
        url: https://correspondence-${env}.zinnia.com/apigateway/rulesengine/v1
    execute:
      api:
        url: ${apiGateway.ruleEngine.base.api.url}/execute
    # Rule engine client code
    client: CORRO

# Kafka configuration
kafka:
  configure: true
  # Kafka bootstrap server address
  bootstrapAddress: ${KAFKA_BOOTSTRAP_SERVER}
  # Kafka authentication key
  key: ${CORRO_KAFKA_KEY}
  # Kafka authentication secret
  secret: ${CORRO_KAFKA_SECRET}

# ZSOR KAFKA configs
zsor:
  kafkaEventAppTopic:
    topicName: ${ZSOR_KAFKA_EVENT_APP_TOPIC_NAME}
    groupId: ${ZSOR_KAFKA_EVENT_APP_TOPIC_GROUP_ID}
    configure: ${kafka.configure}
    bootstrapAddress: ${kafka.bootstrapAddress}
    key: ${kafka.key}
    secret: ${kafka.secret}

# BPM (Business Process Management) configuration
bpm:
  kafkaEventAppTopic:
    topicName: ${BPM_KAFKA_EVENT_APP_TOPIC_TOPIC_NAME}
    groupId: ${BPM_KAFKA_EVENT_APP_TOPIC_GROUP_ID}
    configure: ${kafka.configure}
    bootstrapAddress: ${kafka.bootstrapAddress}
    key: ${kafka.key}
    secret: ${kafka.secret}

# Data Processor configs
dataprocessor:
  kafkaEventAppTopic:
    topicName: ${DATA_PROCESSOR_KAFKA_EVENT_APP_TOPIC_TOPIC_NAME}
    groupId: ${DATA_PROCESSOR_KAFKA_EVENT_APP_TOPIC_GROUP_ID}
    configure: ${kafka.configure}
    bootstrapAddress: ${kafka.bootstrapAddress}
    key: ${kafka.key}
    secret: ${kafka.secret}

# CDS KAFKA configs
cds:
  kafkaEventAppTopic:
    topicName: ${CDS_KAFKA_EVENT_APP_TOPIC_NAME}
    groupId: ${CDS_KAFKA_EVENT_APP_GROUP_ID}
    configure: ${kafka.configure}
    bootstrapAddress: ${kafka.bootstrapAddress}
    key: ${kafka.key}
    secret: ${kafka.secret}

# Timed Corro Config
timedCorroEvent:
  kafkaEventAppTopic:
    topicName: ${TIMED_CORRO_KAFKA_EVENT_APP_TOPIC_NAME}
    groupId: ${TIMED_CORRO_KAFKA_EVENT_APP_GROUP_ID}
    configure: ${kafka.configure}
    bootstrapAddress: ${kafka.bootstrapAddress}
    key: ${kafka.key}
    secret: ${kafka.secret}

# AWS DEV S3 Config details
corro:
  aws:
    bucketName: ${CORRO_AWS_BUCKET_NAME}
    eds:
      folder: policypages/
      prenote: prenotification/
      tao: transferofassets/
    originalRequest: WF_originalEvent
    dpRequest: WF_To_DP_Event
    cdsRequest: WF_To_CDS_Event
    policyData: WF_policyAPIResponse
    transactionData: WF_transactionDetailsAPIResponse
    previousVersionPolicyData: WF_previousVersionPolicyAPIResponse
    planCodeFromPolicyData: WF_planCodeFromPolicyAPIResponse
    searchPartyRefData: WF_searchPartyRefAPIResponse
    eDeliveryPrefData: WF_getEDeliveryPrefAPIResponse
    transactionStoreData: WF_transactionStoreDataAPIResponse
    edsSearchDocs: WF_edsSearchDocumentsDataAPIResponse
    edsDownloadDocs: WF_edsDownloadDocumentsDataAPIResponse
    riskClassificationData: WF_riskClassificationAPIResponse
    pomData: WF_pomDataAPIResponse
    parentPomData: WF_parentPomDataAPIResponse
    carrierData: WF_carrierDataAPIResponse
  cloudberryPath: \\\\cloudberryshare.zinnia.com/
  dataProcessor:
    consumerReadTimeout: 180000

# Logging configuration
logging:
  level:
    # Root logging level
    root: ${CORRO_SERVICE_LOGGING_LEVEL_ROOT}

# Retry Configuration
api:
  retry:
    maxAttempts: 3
    initialBackoffMs: 1000
    backoffMultiplier: 2.0
    maxBackoffMs: 10000
    retryOn5xx: true
    retryOnMongoErrors: true
    mongoMaxAttempts: 3

# Spring Boot Actuator Configuration - Metrics & Monitoring
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: "*"  # Expose all actuator endpoints
  endpoint:
    health:
      show-details: always  # Show detailed health information
      probes:
        enabled: true  # Enable Kubernetes liveness/readiness probes
  health:
    livenessState:
      enabled: true
    readinessState:
      enabled: true
    mongodb:
      enabled: true  # Enable MongoDB health check

# Event Processor Config
event:
  processor:
    polling:
      interval: ${CORRO_SERVICE_EVENT_POLLING_INTERVAL}  # value in milliseconds
