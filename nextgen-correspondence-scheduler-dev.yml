---
env: ${CORRO_ENV}
# Spring application configuration
spring:
  # Name of the Spring application
  application:
    name: nextgen-correspondence-scheduler
  # Apache Kafka Configuration
  kafka:
    # Kafka broker connection endpoints with fallback to local development instance
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVER}
    # Topic name for correspondence events
    topic-name: ${TIMED_CORRO_KAFKA_EVENT_APP_TOPIC_NAME}
    # Kafka producer specific configurations
    producer:
      # Class used to serialize message keys (String format)
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # Class used to serialize message values (JSON format)
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # Acknowledgment strategy - require acknowledgment from all replicas for durability
      acks: all
      security.protocol: SASL_SSL
      sasl.mechanisms: PLAIN
      KAFKA_SASL_USERNAME: ${KAFKA_SASL_USERNAME}
      KAFKA_SASL_PASSWORD: ${KAFKA_SASL_PASSWORD}
      sasl.jaas.config: >
        org.apache.kafka.common.security.plain.PlainLoginModule required
        username="${KAFKA_SASL_USERNAME}"
        password="${KAFKA_SASL_PASSWORD}";
      # Don't fail on errors
      properties:
        retries: 3
        request.timeout.ms: 30000
        max.block.ms: 30000
        delivery.timeout.ms: 120000
        metadata.max.age.ms: 300000
        reconnect.backoff.ms: 1000
        reconnect.backoff.max.ms: 5000
  # MongoDB database configuration
  data:
    mongodb:
      # MongoDB connection URI with environment-specific host and credentials
      uri: mongodb+srv://${CORRO_SCHEDULER_MONGO_USER}:${CORRO_SCHEDULER_MONGO_PASSWORD}@correspondence-${env}-pl-0.qih1s.mongodb.net/?retryWrites=true&w=majority&appName=correspondence-${env}
      # Socket timeout in milliseconds for MongoDB operations
      socketTimeout: 150000
      # MongoDB database name
      database: ${CORRO_SCHEDULER_MONGODB_DATABASE}
      # Enable automatic creation of MongoDB indexes based on entity annotations
      auto-index-creation: true
      # Disable blocking connection checks
      connect-timeout: 1000
      # Don't fail if MongoDB isn't available
      failFast: false

# API Configuration
ruleEngineApi:
  # Authentication endpoints and credentials
  auth:
    url: ${RULE_ENGINE_BASE_URL}/uaam/authenticate
    username: ${CORRO_SERVICE_BPM_RULE_ENGINE_USERNAME}
    password: ${CORRO_SERVICE_BPM_RULE_ENGINE_PASSWORD}
    clientCode: CORRO
  # Rule engine endpoints
  executeUrl: ${RULE_ENGINE_BASE_URL}/ruleengine/v1/businessrule/execute
  # Retry configuration for API calls
  retry:
    maxAttempts: 3
    initialIntervalMs: 1000
    multiplier: 2.0

# Web Server Configuration
server:
  # Port on which the application's web server will listen
  port: 8080
  # Ensure server stays running
  shutdown: graceful

# Spring Boot Actuator Configuration for Monitoring and Health Checks
management:
  # Web endpoint configuration
  endpoints:
    web:
      exposure:
        # List of actuator endpoints to expose via HTTP
        include: health,info,metrics
  # Health endpoint configuration
  endpoint:
    health:
      # Include detailed health information in responses
      show-details: always
      # Don't stop app if health checks fail
      probes:
        enabled: true
  # Component health check configuration
  health:
    # Don't require MongoDB or Kafka health to pass
    livenessState:
      enabled: true
    readinessState:
      enabled: true
    # Enable MongoDB health checks but don't block startup
    mongodb:
      enabled: true
    # Enable Kafka health checks but don't block startup
    kafka:
      enabled: true

# Application-Specific Configuration
application:
  # Scheduler Configuration
  scheduler:
    # Default polling interval if API doesn't provide one
    default-polling-interval-hours: 1
    # Maximum number of retry attempts for failed operations
    max-retry-attempts: 3
    # Initial interval between retry attempts in milliseconds
    retry-initial-interval-ms: 1000
    # Multiplier for exponential backoff between retries
    retry-multiplier: 2

# Datadog configuration
datadog:
  # Enable Datadog integration
  enabled: false
  # Service name to identify this application in Datadog
  service-name: ${spring.application.name}
  # Datadog environment tag (can be overridden via environment variable)
  environment: ${env}
  # Tags to include with metrics and traces
  tags:
    - app:${spring.application.name}
    - service:scheduler
    - version:${app.version:latest}
