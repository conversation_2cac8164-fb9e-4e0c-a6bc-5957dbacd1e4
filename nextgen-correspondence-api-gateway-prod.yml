env: ${CORRO_ENV}
external:
  health:
    enabled: ${CORRO_API_HEALTH_CHECKS}
# Spring application configuration
spring:
  # Name of the Spring application
  application:
    name: nextgen-correspondence-api-gateway
  config:
    # Connect to Spring Cloud Config Server if available
    # import: "optional:configserver:"
    enabled: false
  cache:
    cache-names: myCache
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    date-format: yyyy-MM-dd'T'HH:mm:ss.SSSZ
    time-zone: UTC
  servlet:
    multipart:
      max-file-size: 209715200
      max-request-size: 209715200
      file-size-threshold: 2097152
      enabled: true
      
# File Upload Configuration
file-upload:
  max-file-size: 209715200
  max-request-size: 209715200
  file-size-threshold: 2097152
  max-json-string-length: 209715200
  json-to-multipart-threshold: 10485760
  enable-multipart-upload: true
  enable-chunked-upload: false
  chunk-size: 5242880      

# RestClient Configuration
rest-client:
  timeout: 30
  circuit-breaker:
    failure-rate-threshold: 50
    wait-duration-in-open-state: 60s
    permitted-number-of-calls-in-half-open-state: 2
    sliding-window-size: 10
    minimum-number-of-calls: 5
  retry:
    max-attempts: 3
    wait-duration: 30s
    retry-exceptions: org.springframework.web.client.ResourceAccessException, java.net.ConnectException, org.springframework.web.client.HttpServerErrorException, org.springframework.web.client.HttpClientErrorException
    ignore-exceptions: org.springframework.web.client.HttpClientErrorException$NotFound
  connect-timeout: 30000
  read-timeout: 30000

# Resilience4j Configuration
resilience4j:
  circuitbreaker:
    configs:
      default:
        failureRateThreshold: 50
        waitDurationInOpenState: 60s
        permittedNumberOfCallsInHalfOpenState: 2
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        automaticTransitionFromOpenToHalfOpenEnabled: true
        recordExceptions:
          - org.springframework.web.client.ResourceAccessException
          - java.net.ConnectException
          - org.springframework.web.client.HttpServerErrorException
          - org.springframework.web.client.HttpClientErrorException
        ignoreExceptions:
          - org.springframework.web.client.HttpClientErrorException.NotFound
    instances:
      edsApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      caseStoreApi:
        baseConfig: default
        failureRateThreshold: 45
        waitDurationInOpenState: 45s
      preferenceMgmtApi:
        baseConfig: default
        failureRateThreshold: 35
        waitDurationInOpenState: 60s
      authTokenApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      agentApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      carrierApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      caseManagementApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      correspondenceDocumentApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      riskClassApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      kafkaProducerApi:
        baseConfig: default
        failureRateThreshold: 30
        waitDurationInOpenState: 45s
      awsFileDownloadApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      awsFileUploadApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s  
      s3UploadCircuitBreaker:
        baseConfig: default
        failureRateThreshold: 50
        waitDurationInOpenState: 60s
      s3ListObjectsCircuitBreaker:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      s3DownloadFilesCircuitBreaker:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      s3DownloadFileCircuitBreaker:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      downloadJsonFilesFromS3CircuitBreaker:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      downloadXmlContentFromS3CircuitBreaker:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      s3DownloadXmlFilesCircuitBreaker:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      zaharaApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      enterpriseEmailServiceApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      sftpApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      transactionDataStoreApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      fundsApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s
      productRateApi:
        baseConfig: default
        failureRateThreshold: 40
        waitDurationInOpenState: 30s      
  retry:
    configs:
      default:
        maxAttempts: 4
        waitDuration: 1s
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2
        retryExceptions:
          - org.springframework.web.client.ResourceAccessException
          - java.net.ConnectException
          - org.springframework.web.client.HttpServerErrorException
          - org.springframework.web.client.HttpClientErrorException
          - com.zinnia.nextgen.correspondence.common.exception.ApiException
          - com.zinnia.nextgen.correspondence.common.exception.ExternalApiResponseException
          - com.zinnia.nextgen.correspondence.common.exception.RestApiTerminalException
          - com.zinnia.nextgen.correspondence.common.retry.RestApiRetryableException
        # Enable retry event logging
        enableRetryEventLogging: true
    instances:
      edsApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 2s
      caseStoreApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      preferenceMgmtApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 30s
      authTokenApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      agentApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      carrierApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      caseManagementApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      correspondenceDocumentApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      riskClassApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      kafkaProducerApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 2s
      awsFileDownloadApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      s3UploadRetry:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 2s
      s3ListObjectsRetry:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      s3DownloadFilesRetry:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      s3DownloadFileRetry:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      downloadJsonFilesFromS3Retry:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      downloadXmlContentFromS3Retry:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      s3DownloadXmlFilesRetry:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s  
      zaharaApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      awsFileUploadApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s  
      ruleEngineApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      enterpriseEmailServiceApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      sftpApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 2s
      fgaTokenRetry:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      rulesEngineTokenRetry:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      transactionDataStoreApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      fundsApi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s
      productRateAPi:
        baseConfig: default
        maxAttempts: 4
        waitDuration: 1s      
  metrics:
    enabled: true
    tags:
      application: ${spring.application.name}
      environment: ${env}

# Rule Engine configuration
ruleengine:
  api:
    base:
      # Rule engine base URL
      url: ${RULE_ENGINE_BASE_URL}
    execute:
      rule:
        # Rule execution endpoint URL
        url: ${ruleengine.api.base.url}/ruleengine/v1/businessrule/execute
    get:
      group:
        rules: ${ruleengine.api.base.url}/ruleengine/v1/group/externalId/{groupExternalId}/association/search
    authenticate:
      # Authentication endpoint URL
      url: ${ruleengine.api.base.url}/uaam/authenticate
      username: ${CORRO_SERVICE_BPM_RULE_ENGINE_USERNAME}
      password: ${CORRO_SERVICE_BPM_RULE_ENGINE_PASSWORD}
      clientCode: ${CORRO_SERVICE_BPM_RULE_ENGINE_CLIENT_CODE:CORRO}

# Processed files configuration
app:
  cache:
    rule:
      refresh:
        interval: PT24H
    ruleresult:
      refresh:
        interval: PT24H

# EDS Document
eds:
  base:
    url: ${EDS_DOCUMENT_BASE_URL}
  api:
    search:
      url: ${eds.base.url}/search?limit=10&offset=0&x-correlation-id={correlationId}
      documentClassification: INBOUND
      documentType: POLPGFORM
    download:
      url: ${eds.base.url}/{documentId}/download

# Case Store Data
caseStore:
  api:
    base:
      url: ${CASE_STORE_BASE_URL}
      
# Transaction Data Store Data
transactionDataStore:
  getRecordIdData:
    url: ${api.paths.enterprise}/transactions/v1/transaction/entities/{recordId}
    
# Product Rate Data
productRateData:
    url: ${api.paths.enterprise}/product-rate/v1/carriers/{carrierCode}/products/{planCode}/benefits/{benefitId}/configured-settings/{configuredSetting}?effectiveDate={effectiveDate}&coverageAmount={coverageAmount}&issueAge={issueAge}&contractYear={contractYear}&guaranteePeriodYear={guaranteePeriodYear}
    

# Fund Data Management API
funds:
  api:
    planCode:
      url: ${api.paths.enterprise}/funds/v1/carriers/{carrierID}/products/{planCode}
    fundId:
      url: ${api.paths.enterprise}/funds/v1/carriers/{carrierID}/funds/{fundId}

# Illustrations API
illustrations:
  api:
    quoteId:
      url: ${api.paths.enterprise}/illustration/v3/illustration-request/{quoteId}/results/RAW_ILLUSTRATION_VALUES    

# Carrier Data
carrier:
  api:
    base:
      url: ${api.paths.enterprise}/policy/v1/carriers/{carrierID} 

# Agent Data
agent:
  api:
    base:
      url: ${api.paths.enterprise}/api/{clientCode}/salesentity?idtype={idType}&id={idValue}&skip=0&take=10
  pomapi:
    base:
      url: ${api.paths.enterprise}/distributors/v1/producers/search?sortBy=&sortOrder=&limit=10&offset=0

# Zinnia Live Case Management API
casemanagement:
  api:
    base:
      url: ${api.paths.enterprise}/case/v1/cases/{caseID}
    searchByQuoteId:
      url: ${api.paths.enterprise}/case/v1/cases/search  

# Zinnia Enterprise Email Service API
enterpriseEmailService:
  api:
    base:
      url: ${api.paths.enterprise}/communication/v1/email                       

# Preference Management
preferences:
  api:
    base:
      url: ${PREFERENCE_MANAGEMENT_BASE_URL}
  getepreferences:
    url: ${preferences.api.base.url}/v1/{partyId}/e-delivery/{planCode}/{policyNumber}?documentType={documentType}
  policyReceipt:
    docType: NWB

# Party Reference
partyreference:
  api:
    base:
      url: ${PARTY_REFERENCE_BASE_URL}
  searchparties:
    url: ${partyreference.api.base.url}/v1/parties/reference/search?offset=0&limit=10

# Zahara
zahara:
  api:
    base:
      url: ${ZAHARA_POLICY_BASE_URL}
  policyDetails:
    inputversion:
      url: ${zahara.api.base.url}/v1/policies/{planCode}/{policyNumber}?version={version}&viewDetails=true
    latestversion:
      url: ${zahara.api.base.url}/v1/policies/{planCode}/{policyNumber}?viewDetails=true
  transactionDetails:
    url: ${zahara.api.base.url}/v1/policies/{planCode}/{policyNumber}/transactions/{transactionId}
  riskClassDetails:
    url: ${ENTERPRISE_API_BASE_URL}/policy/v1/carriers/{carrierCode}/products/{planCode}/translations
  policySearch:
    url: ${ENTERPRISE_API_BASE_URL}/policy/v1/policies/search  
 

# Cache refresh configuration
cache:
  # Cache lifecycle management
  lifecycle:
    # Startup behavior
    startup:
      # Initialize cache gracefully on startup
      graceful-init: true
      # Wait for cache to be ready before accepting requests
      wait-for-ready: false
      # Log cache initialization status
      log-init-status: true
    # Shutdown behavior
    shutdown:
      # Gracefully shutdown cache on application stop
      graceful-shutdown: true
      # Save cache state if possible
      persist-state: false
      # Log shutdown status
      log-shutdown-status: true
  # Cache resilience and monitoring
  ttl:
    minutes: 60
  maxSize: 1000
  # Cache resilience and monitoring
  resilience:
    # Handle cache eviction gracefully
    eviction-handling:
      enabled: true
      # Log eviction events for monitoring
      log-evictions: true
      # Notify when cache size approaches limit
      size-warning-threshold: 0.8  # 80% of maxSize
    # Cache health monitoring
    health-monitoring:
      enabled: true
      # Monitor cache hit/miss ratios
      track-hit-ratios: true
      # Alert on poor cache performance
      performance-threshold: 0.6  # 60% hit ratio threshold
      # Monitor memory usage
      memory-monitoring: true
  refresh:
    # Cache refresh interval in minutes
    interval: 30
    # Refresh behavior rules
    behavior:
      # Normal refresh: every 30 minutes when cache has data
      normal-interval: 30
      # Empty cache handling: skip refresh but maintain normal interval when data exists
      empty-cache-strategy: "skip-refresh-maintain-interval"
      # When cache gets data after being empty, immediately resume 30-minute refresh
      resume-normal-refresh-on-data: true
    # Handle empty cache scenarios gracefully
    empty-cache-handling:
      enabled: true
      # Skip refresh cycles when cache is empty (saves resources)
      skip-empty-refresh: true
      # Log empty cache scenarios for monitoring
      log-empty-scenarios: true
      # Maximum consecutive empty refresh cycles before reducing frequency
      max-empty-cycles: 3
      # Reduced refresh frequency when cache is empty (in minutes) - only for monitoring/logging
      # NOTE: This does NOT change the actual refresh interval - it's just for tracking purposes
      reduced-interval: 120
      # Always maintain 30-minute refresh when cache has data
      maintain-normal-interval-when-data-exists: true
    # Graceful degradation settings
    graceful-degradation:
      enabled: true
      # Continue normal operation even if refresh fails
      continue-on-refresh-failure: true
      # Use stale data if refresh fails (within TTL limits)
      allow-stale-data: true
      # Log refresh failures for monitoring
      log-refresh-failures: true
    # Error handling and fallbacks
    error-handling:
      enabled: true
      # Retry failed refresh attempts
      retry-failed-refresh: true
      # Maximum retry attempts for refresh failures
      max-refresh-retries: 2
      # Fallback to last known good data if refresh fails
      fallback-to-last-known: true
      # Circuit breaker for refresh operations
      circuit-breaker:
        enabled: true
        failure-threshold: 5
        recovery-timeout: 300  # 5 minutes

# Retry Configuration
api:
  paths:
    enterprise: ${ENTERPRISE_API_BASE_URL}
  retry:
    maxAttempts: 3
    initialBackoffMs: 30000
    backoffMultiplier: 2.0
    maxBackoffMs: 10000
    retryOn5xx: true

# Enterprise API Configuration
enterprise-api:
  timeout: 30
  max-retries: 3

# Spring Boot Actuator Configuration - Metrics & Monitoring
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: "*"
      discovery:
        enabled: false
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        readiness:
          include: readinessState,diskSpace,externalServices
        liveness:
          include: livenessState,ping
    metrics:
      enabled: true
    circuitbreakers:
      enabled: true
    retries:
      enabled: true
    prometheus:
      enabled: true
  health:
    livenessState:
      enabled: true
    readinessState:
      enabled: true
    defaults:
      enabled: true
    diskspace:
      enabled: true
    circuitbreakers:
      enabled: true
    config:
      enabled: false
    discovery:
      enabled: false
  metrics:
    tags:
      application: ${spring.application.name}
      environment: ${env}
    distribution:
      percentiles-histogram:
        http.server.requests: true
        resilience4j.circuitbreaker.calls: true
        resilience4j.retry.calls: true
        http.client.requests: true
      sla:
        http.server.requests: 10ms, 50ms, 100ms, 200ms, 500ms
        resilience4j.circuitbreaker.calls: 10ms, 50ms, 100ms, 200ms, 500ms
        resilience4j.retry.calls: 10ms, 50ms, 100ms, 200ms, 500ms
        http.client.requests: 10ms, 50ms, 100ms, 200ms, 500ms
  tracing:
    sampling:
      probability: 1.0
    propagation:
      type: B3
    enabled: true

# Auth Token
auth:
  apiUrl: ${ENTERPRISE_API_AUTH_ENDPOINT}
  audience: ${api.paths.enterprise}
  grantType: client_credentials
  client_id: ${ENTERPRISE_API_CLIENT}
  client_secret: ${ENTERPRISE_API_SECRET}

# AWS Configuration
corro:
  aws:
    accessKey: ${CORRO_AWS_ACCESS_KEY}
    secretKey: ${CORRO_AWS_SECRET_KEY}
    region: ${CORRO_AWS_REGION}
    # Retry configuration for AWS SDK
    baseDelayMillis: ${CORRO_AWS_BASE_DELAY_MILLIS:1000}
    maxBackoffSeconds: ${CORRO_AWS_MAX_BACKOFF_SECONDS:10}
    numRetries: ${CORRO_AWS_NUM_RETRIES:3}
  cache:
    hit-rate-threshold: 0.8  # Cache hit rate threshold for logging stats (80%)

server:
  tomcat:
    max-threads: 200
    min-spare-threads: 20
    max-connections: 10000
    accept-count: 100
    connection-timeout: 30000
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css
    min-response-size: 1024

# RestTemplate configuration
rest:
  template:
    connect-timeout: 30000
    read-timeout: 30000
    max-total-connections: 2600
    max-connections-per-route: 650
    connection-time-to-live: 120
    connection-request-timeout: 30000

# Supported Application for which we need to send the request to the external service
supported-applications:
  applications:
    - nextgen-correspondence-service
    - nextgen-correspondence-email

# Logging configuration
logging:
  level:
    # Root logging level
    root: ${CORRO_API_GATEWAYSERVICE_LOGGING_LEVEL_ROOT:info}
     # Retry logging configuration
    com.zinnia.nextgen.correspondence.common.retry: info
    io.github.resilience4j.retry: info

# health check for actuator
health:
  url:
    ruleEngine: ${api.paths.enterprise}/actuator/health
    zahara: ${api.paths.enterprise}/policy/policies/health
    partyReference: ${api.paths.enterprise}/party/parties/health
    
# Kafka Configuration
kafka:
  bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVER}
  key: ${CORRO_KAFKA_KEY}
  secret: ${CORRO_KAFKA_SECRET}
  security:
    protocol: SASL_SSL
    mechanism: PLAIN
    jaas-config-template: "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";"
  producer:
    key-serializer: org.apache.kafka.common.serialization.StringSerializer
    value-serializer: org.apache.kafka.common.serialization.StringSerializer
    acks: all
    retries: 3
    batch-size: 16384
    linger-ms: 1
    buffer-memory: 33554432
    request-timeout-ms: 30000
    delivery-timeout-ms: 120000

# SFTP Configuration
sftp:
  host: ${MERRILL_SFTP_HOST:mcsftp.toppanmerrill.com}
  port: 22
  username: ${MERRILL_SFTP_USERNAME:se2_secben}
  password: ${MERRILL_SFTP_PASSWORD:KGr4yslQliS7}
  max-retries: 3
  retry-delay-ms: 15000
  connection-timeout-ms: 30000
  session-timeout-ms: 300000
  # Path to known_hosts file for SSH key verification (optional)
  # If not specified, will use ~/.ssh/known_hosts or fall back to AcceptAllServerKeyVerifier
  known-hosts-path: ${MERRILL_SFTP_KNOWN_HOSTS_PATH:/app/known_hosts}     
    
    