---
env: ${CORRO_ENV}
# Spring application configurations
spring:
  # Name of the Spring application
  application:
    name: nextgen-correspondence-email

# Correspondence email service configuration
correspondence:
  email:
    # Kafka consumer configuration
    kafka:
      consumer:
        # Kafka bootstrap server address
        bootstrapAddress: ${KAFKA_BOOTSTRAP_SERVER}
        # Consumer group configuration
        group:
          # Consumer group ID for outgoing transactions
          id: zinnia.cds.transactions.outgoing-${env}
        # Kafka authentication key
        key: ${CORRO_EMAIL_KAFKA_CONSUMER_KEY}
        # Kafka authentication secret
        secret: ${CORRO_EMAIL_KAFKA_CONSUMER_SECRET}
        # Number of concurrent consumers
        concurrency: 3
        # Kafka topic for outgoing transactions
        topic: zinnia.cds.transactions.outgoing
      # Kafka heartbeat configuration
      heartbeat:
        interval:
          # Heartbeat interval in milliseconds
          ms: 20000
      # Kafka session configuration
      session:
        timeout:
          # Session timeout in milliseconds
          ms: 60000
      # Kafka auto-commit configuration
      auto:
        commit:
          interval:
            # Auto-commit interval in milliseconds
            ms: 10000
      # Kafka max poll configuration
      max:
        poll:
          interval:
            # Maximum poll interval in milliseconds
            ms: 3000000
          # Maximum number of records per poll
          records: 50
    # E-Notification consumer configuration
    eNotification:
      consumer:
        # Kafka topic for incoming transactions
        topic: ${CDS_KAFKA_EVENT_APP_TOPIC_NAME}

# Application configuration
app:
  # Processed files configuration
  processed:
    email:
      delivery:
        # Folder for processed email delivery files
        folder: src/main/resources/payloads/processed/emaildelivery
    notification:
      # Folder for processed notification files
      folder: src/main/resources/payloads/processed/emailnotification
    summary:
      # Summary report filename
      filename: summary-report.txt
  # Email configuration
  email:
    delivery:
      api:
        # Email delivery API URL
        url: http://localhost:8080/api/v1/email/delivery/test
      input:
        # Input folder for email delivery test payloads
        folder: classpath:payloads/testEDelivery/*.json
    notification:
      api:
        # Email notification API URL
        url: http://localhost:8080/api/v1/email/notification/test
      input:
        # Input folder for email notification test payloads
        folder: classpath:payloads/testENotification/*.json
    test:
      run:
        # Flag to enable/disable email test runs
        active: false
  # API Gateway configuration
  apigateway:
    api:
      # API Gateway base URL
      base-url: https://correspondence.zinnia.com/apigateway/api/v1
      # API Gateway agent details endpoint
      agent:
        details:
          endpoint: /agent/details
      # API Gateway policy endpoint
      policy:
        endpoint: /zahara/policy/latest
      # API Gateway case transaction endpoint
      casetransaction:
        endpoint: /casestore/search
      # API Gateway transaction data store endpoint for record ID data
      transactiondatastore:
        endpoint: /transactiondatastore/getRecordIdData
      # API Gateway case endpoint
      case:
        endpoint: /case-management/details
      # API Gateway carrier endpoint
      carrier:
        endpoint: /carrier/details
      # API Gateway preferences endpoint for e-delivery preferences
      preferences:
        endpoint: /preferences/edelivery
      # API Gateway party reference endpoint
      party-reference:
        endpoint: /preferences/partyreference
      # API Gateway enterprise email service endpoint
      enterprise-email:
        endpoint: /enterprise-email-service/send-email
      # API Gateway BPM rule engine endpoint
      rulesengine:
        endpoint: /rulesengine/v1/execute
      # API Gateway AWS upload endpoint
      aws:
        upload:
          endpoint: /aws/upload
      # Application header for API Gateway requests
      application-header: nextgen-correspondence-email
      # S3 bucket name for storage
      s3-bucket-name: strategiccorrespondence${env}

# Rule Engine configuration
ruleengine:
  api:
    # Rule engine client code
    client: CORRO
  rule:
    emailtemplate:
      # Email template lookup rule name
      name: EmailTemplateLookup
    carrier:
      # Carrier lookup rule name
      name: CarrierLookup
    doctype:
      # Document type lookup rule name
      name: DocumentTypeLookup

# Request/Response configuration
request:
  response:
    # Audit log configuration
    auditLog: ${CORRO_EMAIL_REQUEST_RESPONSE_AUDITLOG}

# Logging configuration
logging:
  level:
    # Root logging level
    root: ${CORRO_EMAIL_LOGGING_LEVEL_ROOT}

# E-Notification configuration
eNotification:
  allowed:
    # List of allowed clients for e-notification
    clients: SBGC,WELB,ARIC,ILNA,USAA,FNWL,EVGL
  agent:
    welcome:
      # Event type for agent welcome notifications
      event: producer.ciamOnboarding.completed
  ofac:
    event: task.ofacreview.created
  suitability:
    review:
      event: task.suitabilityreview.created
  nigo:
    exception:
      event: task.suitabilitydataentrynigoreview.created

# Reminder email configuration
reminder:
  email:
    # Document type for reminder emails
    docType: DRRME

# Final reminder email configuration
final:
  reminder:
    email:
      # Document type for final reminder emails
      docType: FDRRME

# Agent welcome configuration
agent:
  welcome:
    # Event that triggers agent welcome
    event: producer.ciamOnboarding.completed
    # Document type for agent welcome
    docType: AGTWCM

# Suitability decline configuration
suitability:
  decline:
    # Document type for suitability decline
    docType: SUTDCL
  review:
    # Document type for suitability review
    docType: ASUTRW

# Ofac configuration
ofac:
  docType: OFAC

nigoException:
  preissue:
    docType: NSUTDT
  postissue:
    docType: NIWTD,NIROL,NIBEN,NISERV # <-Farmers & KUV Doc Types