name: Deployment Jobs
permissions:
  id-token: write
  contents: read
on:
  workflow_dispatch:
jobs:
  DeployFNWL-TRAINING:
    runs-on: ubuntu-latest
    environment: fnwl-training
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Checkout correspondence DevOps Repository
        uses: actions/checkout@v4
        with:
          repository: zinnia/correspondence-devops
          path: correspondence-devops
          ref: refs/heads/main
          token: ${{ secrets.GIT_TOKEN }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.ASSUME_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Run cloudformation scripts
        run: |
          ls -lrth
          bash correspondence-devops/cloudformation/run.sh CONFIG_COPY || exit 1
        env:
          ENVIRONMENT: ${{ vars.ENVIRONMENT }}
          CONFIG_BUCKET: ${{ vars.CONFIG_BUCKET }}
  DeployDEMO:
    runs-on: ubuntu-latest
    environment: demo
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Checkout correspondence DevOps Repository
        uses: actions/checkout@v4
        with:
          repository: zinnia/correspondence-devops
          path: correspondence-devops
          ref: refs/heads/main
          token: ${{ secrets.GIT_TOKEN }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.ASSUME_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Run cloudformation scripts
        run: |
          ls -lrth
          bash correspondence-devops/cloudformation/run.sh CONFIG_COPY || exit 1
        env:
          ENVIRONMENT: ${{ vars.ENVIRONMENT }}
          CONFIG_BUCKET: ${{ vars.CONFIG_BUCKET }}
  DeployDEV:
    runs-on: ubuntu-latest
    environment: dev
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Checkout correspondence DevOps Repository
        uses: actions/checkout@v4
        with:
          repository: zinnia/correspondence-devops
          path: correspondence-devops
          ref: refs/heads/main
          token: ${{ secrets.GIT_TOKEN }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.ASSUME_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Run cloudformation scripts
        run: |
          ls -lrth
          bash correspondence-devops/cloudformation/run.sh CONFIG_COPY || exit 1
        env:
          ENVIRONMENT: ${{ vars.ENVIRONMENT }}
          CONFIG_BUCKET: ${{ vars.CONFIG_BUCKET }}
  DeployQA:
    needs: DeployDEV
    runs-on: ubuntu-latest
    environment: qa
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Checkout correspondence DevOps Repository
        uses: actions/checkout@v4
        with:
          repository: zinnia/correspondence-devops
          path: correspondence-devops
          ref: refs/heads/main
          token: ${{ secrets.GIT_TOKEN }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.ASSUME_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Run cloudformation scripts
        run: |
          bash correspondence-devops/cloudformation/run.sh CONFIG_COPY
        env:
          ENVIRONMENT: ${{ vars.ENVIRONMENT }}
          CONFIG_BUCKET: ${{ vars.CONFIG_BUCKET }}
  DeployUAT:
    needs: DeployQA
    runs-on: ubuntu-latest
    environment: uat
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Checkout correspondence DevOps Repository
        uses: actions/checkout@v4
        with:
          repository: zinnia/correspondence-devops
          path: correspondence-devops
          ref: refs/heads/main
          token: ${{ secrets.GIT_TOKEN }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.ASSUME_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Run cloudformation scripts
        run: |
          bash correspondence-devops/cloudformation/run.sh CONFIG_COPY
        env:
          ENVIRONMENT: ${{ vars.ENVIRONMENT }}
          CONFIG_BUCKET: ${{ vars.CONFIG_BUCKET }}
  DeployPROD:
    needs: DeployUAT
    runs-on: ubuntu-latest
    environment: prod
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Checkout correspondence DevOps Repository
        uses: actions/checkout@v4
        with:
          repository: zinnia/correspondence-devops
          path: correspondence-devops
          ref: refs/heads/main
          token: ${{ secrets.GIT_TOKEN }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.ASSUME_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Run cloudformation scripts
        run: |
          bash correspondence-devops/cloudformation/run.sh CONFIG_COPY
        env:
          ENVIRONMENT: ${{ vars.ENVIRONMENT }}
          CONFIG_BUCKET: ${{ vars.CONFIG_BUCKET }}
