---
env: ${CORRO_ENV}
# Server Configuration
server:
  port: 8080

# Template Engine Configuration
spring:
  # Name of the Spring application
  application:
    name: nextgen-correspondence-templates
  config:
    # Connect to Spring Cloud Config Server if available
    import: "optional:configserver:"
  thymeleaf:
    cache: false
    prefix: classpath:/view/
    suffix: .html
    mode: HTML
    encoding: UTF-8
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      
# Document Configuration
document:
  base-path: ${CORRO_DOCUMENT_BASE_PATH:/document}      

# Logging Configuration
logging:
  level:
    root: ${CORRO_TEMPLATE_LOGGING_LEVEL_ROOT}
    com.example.documentprocessor: ${CORRO_DOCUMENT_PROCESSOR_LOGGING_LEVEL_ROOT}
    org.springframework.cloud.config: ${CORRO_SPRING_CONFIG_LOGGING_LEVEL_ROOT}
# Template Directory Configuration
template:
  directory: templates/
  source: ${CORRO_TEMPLATE_SOURCE:s3} # Can be 's3' or 'local'
# Adobe PDF Services Configuration
adobe:
  pdfservices:
    client:
      id: ${CORRO_ADOBE_CLIENT_ID}
      secret: ${CORRO_ADOBE_SECRET_ID}
# AWS Configuration
aws:
  accessKey: ${CORRO_AWS_ACCESS_KEY}
  secretKey: ${CORRO_AWS_SECRET_KEY}
  region: ${CORRO_AWS_REGION}
