# Correspondence Configuration Documentation

This repository contains configuration files for various correspondence services in different environments. All configurations are in YAML format.

## Table of Contents
- [Email Service Configuration](#email-service-configuration)
- [Service Configuration](#service-configuration)
- [Scheduler Configuration](#scheduler-configuration)
- [Environment Variables](#environment-variables)

## Email Service Configuration

### Spring Configuration
```yaml
spring:
  application:
    name: nextgen-correspondence-email
  data:
    mongodb:
      uri: mongodb+srv://${CORRO_EMAIL_MONGO_USER}:${CORRO_EMAIL_MONGO_PASSWORD}@correspondence-{env}-pl-0.qih1s.mongodb.net/?retryWrites=true&w=majority&appName=correspondence-{env}
      socketTimeout: 150000
      database: correspondence-email
```
| Property | Description | Default |
|----------|-------------|---------|
| `spring.application.name` | Name of the Spring application | nextgen-correspondence-email |
| `spring.data.mongodb.uri` | MongoDB connection URI with environment-specific host | mongodb+srv://... |
| `spring.data.mongodb.socketTimeout` | Socket timeout in milliseconds | 150000 |
| `spring.data.mongodb.database` | MongoDB database name | correspondence-email |

### Correspondence Email Configuration
```yaml
correspondence:
  email:
    kafka:
      consumer:
        bootstrapAddress: pkc-ymrq7.us-east-2.aws.confluent.cloud:9092
        group:
          id: zinnia.cds.transactions.outgoing-{env}
        key: ${CORRO_EMAIL_KAFKA_CONSUMER_KEY}
        secret: ${CORRO_EMAIL_KAFKA_CONSUMER_SECRET}
        concurrency: 1
        topic: zinnia.cds.transactions.outgoing
      heartbeat:
        interval:
          ms: 20000
      session:
        timeout:
          ms: 60000
      auto:
        commit:
          interval:
            ms: 10000
      max:
        poll:
          interval:
            ms: 3000000
          records: 50
    eNotification:
      consumer:
        topic: zinnia.cds.transactions.incoming
```
| Property | Description | Default |
|----------|-------------|---------|
| `correspondence.email.kafka.consumer.bootstrapAddress` | Kafka bootstrap server address | pkc-ymrq7.us-east-2.aws.confluent.cloud:9092 |
| `correspondence.email.kafka.consumer.group.id` | Consumer group ID | zinnia.cds.transactions.outgoing-{env} |
| `correspondence.email.kafka.consumer.key` | Kafka authentication key | ${CORRO_EMAIL_KAFKA_CONSUMER_KEY} |
| `correspondence.email.kafka.consumer.secret` | Kafka authentication secret | ${CORRO_EMAIL_KAFKA_CONSUMER_SECRET} |
| `correspondence.email.kafka.consumer.concurrency` | Number of concurrent consumers | 1 |
| `correspondence.email.kafka.consumer.topic` | Kafka topic for outgoing transactions | zinnia.cds.transactions.outgoing |
| `correspondence.email.kafka.heartbeat.interval.ms` | Kafka heartbeat interval | 20000 |
| `correspondence.email.kafka.session.timeout.ms` | Kafka session timeout | 60000 |
| `correspondence.email.kafka.auto.commit.interval.ms` | Auto-commit interval | 10000 |
| `correspondence.email.kafka.max.poll.interval.ms` | Maximum poll interval | 3000000 |
| `correspondence.email.kafka.max.poll.records` | Maximum records per poll | 50 |
| `correspondence.email.eNotification.consumer.topic` | Kafka topic for incoming transactions | zinnia.cds.transactions.incoming |

### CIAM Configuration
```yaml
ciam:
  token-url: https://login.{env}.zinnia.com/oauth/token
  audience-url: https://{env}.api.zinnia.io
  client-id: ${CORRO_EMAIL_CIAM_CLIENT_ID}
  secret-key: ${CORRO_EMAIL_CIAM_CLIENT_SECRET}
  token-generation-frequency: 22
```
| Property | Description | Default |
|----------|-------------|---------|
| `ciam.token-url` | CIAM token endpoint | https://login.{env}.zinnia.com/oauth/token |
| `ciam.audience-url` | CIAM audience URL | https://{env}.api.zinnia.io |
| `ciam.client-id` | CIAM client ID | ${CORRO_EMAIL_CIAM_CLIENT_ID} |
| `ciam.secret-key` | CIAM client secret | ${CORRO_EMAIL_CIAM_CLIENT_SECRET} |
| `ciam.token-generation-frequency` | Token refresh frequency in hours | 22 |

### Token Configuration
```yaml
token:
  cache:
    timeout: 22
```
| Property | Description | Default |
|----------|-------------|---------|
| `token.cache.timeout` | Token cache timeout in hours | 22 |

### Retry Configuration
```yaml
retry:
  backoff:
    delay: 2000
  max:
    attempts: 3
```
| Property | Description | Default |
|----------|-------------|---------|
| `retry.backoff.delay` | Retry delay in milliseconds | 2000 |
| `retry.max.attempts` | Maximum number of retry attempts | 3 |

### Application Configuration
```yaml
app:
  policy:
    data:
      api:
        base-url: https://{env}.api.zinnia.io/policy/v1/policies
        fga:
          call: true
        view-details: true
  carrier:
    api:
      base-url: https://{env}.api.zinnia.io/policy/v1/carriers
  mcs:
    api:
      base-url: https://{env}.api.zinnia.io/api
      param:
        idtype: external
        fetchNumber: 1
  prefmgmt:
    data:
      api:
        base-url: https://{env}.api.zinnia.io/preferences/v1
  partyRef:
    data:
      api:
        base-url: https://{env}.api.zinnia.io/party/v1/parties/reference/search
        offset: 0
      limit: 10
  email:
    delivery:
      api:
        url: https://{env}.api.zinnia.io/api/v1/email/delivery/test
      input:
        folder: classpath:payloads/testEDelivery/*.json
    notification:
      api:
        url: https://{env}.api.zinnia.io/api/v1/email/notification/test
      input:
        folder: classpath:payloads/testENotification/*.json
    test:
      run:
        active: false
  case:
    data:
      api:
        base-url: https://{env}.api.zinnia.io/case/v1/cases
    transaction:
      api:
        base-url: https://{env}-bpm.se2.com/transactions/v1/transaction/search
  processed:
    email:
      delivery:
        folder: src/main/resources/payloads/processed/emaildelivery
    notification:
      folder: src/main/resources/payloads/processed/emailnotification
    summary:
      filename: summary-report.txt
```
| Property | Description | Default |
|----------|-------------|---------|
| `app.policy.data.api.base-url` | Policy API base URL | https://{env}.api.zinnia.io/policy/v1/policies |
| `app.policy.data.api.fga.call` | Enable FGA calls | true |
| `app.policy.data.api.view-details` | Enable view details | true |
| `app.carrier.api.base-url` | Carrier API base URL | https://{env}.api.zinnia.io/v1/carriers |
| `app.mcs.api.base-url` | MCS API base URL | https://{env}.api.zinnia.io/api |
| `app.mcs.api.param.idtype` | MCS API ID type | external |
| `app.mcs.api.param.fetchNumber` | MCS API fetch number | 1 |
| `app.prefmgmt.data.api.base-url` | Preference management API base URL | https://{env}.api.zinnia.io/preferences/v1 |
| `app.partyRef.data.api.base-url` | Party reference API base URL | https://{env}.api.zinnia.io/party/v1/parties/reference/search |
| `app.partyRef.data.api.offset` | Party reference API offset | 0 |
| `app.partyRef.data.limit` | Party reference API limit | 10 |
| `app.email.delivery.api.url` | Email delivery API URL | https://{env}.api.zinnia.io/api/v1/email/delivery/test |
| `app.email.delivery.input.folder` | Email delivery input folder | classpath:payloads/testEDelivery/*.json |
| `app.email.notification.api.url` | Email notification API URL | https://{env}.api.zinnia.io/api/v1/email/notification/test |
| `app.email.notification.input.folder` | Email notification input folder | classpath:payloads/testENotification/*.json |
| `app.email.test.run.active` | Enable email test run | false |
| `app.case.data.api.base-url` | Case API base URL | https://{env}.api.zinnia.io/case/v1/cases |
| `app.case.transaction.api.base-url` | Case transaction API base URL | https://{env}-bpm.se2.com/transactions/v1/transaction/search |
| `app.processed.email.delivery.folder` | Processed email delivery folder | src/main/resources/payloads/processed/emaildelivery |
| `app.processed.notification.folder` | Processed notification folder | src/main/resources/payloads/processed/emailnotification |
| `app.processed.summary.filename` | Summary report filename | summary-report.txt |

### Rule Engine Configuration
```yaml
ruleengine:
  api:
    base:
      url: https://{env}-bpm.se2.com
    enpoint:
      url: ${ruleengine.api.base.url}/otr-web/api/v1/library/rule/execute?testMode=true
    authenticate:
      url: ${ruleengine.api.base.url}/uaam/authenticate
      username: ${CORRO_EMAIL_BPM_RULE_ENGINE_USERNAME}
      password: ${CORRO_EMAIL_BPM_RULE_ENGINE_PASSWORD}
      client: CORRO
    client: CORRO
  rule:
    emailtemplate:
      name: QAEmailTemplateLookup
    carrier:
      name: CarrierLookup
    doctype:
      name: DocumentTypeLookup
```
| Property | Description | Default |
|----------|-------------|---------|
| `ruleengine.api.base.url` | Rule engine base URL | https://{env}-bpm.se2.com |
| `ruleengine.api.enpoint.url` | Rule engine endpoint URL | ${ruleengine.api.base.url}/otr-web/api/v1/library/rule/execute?testMode=true |
| `ruleengine.api.authenticate.url` | Rule engine authentication URL | ${ruleengine.api.base.url}/uaam/authenticate |
| `ruleengine.api.authenticate.username` | Rule engine username | ${CORRO_EMAIL_BPM_RULE_ENGINE_USERNAME} |
| `ruleengine.api.authenticate.password` | Rule engine password | ${CORRO_EMAIL_BPM_RULE_ENGINE_PASSWORD} |
| `ruleengine.api.authenticate.client` | Rule engine client | CORRO |
| `ruleengine.api.client` | Rule engine client code | CORRO |
| `ruleengine.rule.emailtemplate.name` | Email template rule name | QAEmailTemplateLookup |
| `ruleengine.rule.carrier.name` | Carrier rule name | CarrierLookup |
| `ruleengine.rule.doctype.name` | Document type rule name | DocumentTypeLookup |

### BPM Configuration
```yaml
bpm:
  token:
    cache:
      timeout: 11
```
| Property | Description | Default |
|----------|-------------|---------|
| `bpm.token.cache.timeout` | BPM token cache timeout in hours | 11 |

### Enterprise Email Configuration
```yaml
enterprise:
  email:
    service:
      url: https://{env}.api.zinnia.io/communication/v1/email
```
| Property | Description | Default |
|----------|-------------|---------|
| `enterprise.email.service.url` | Enterprise email service URL | https://{env}.api.zinnia.io/communication/v1/email |

### Request Response Configuration
```yaml
request:
  response:
    auditLog: ${CORRO_EMAIL_REQUEST_RESPONSE_AUDITLOG}
```
| Property | Description | Default |
|----------|-------------|---------|
| `request.response.auditLog` | Request response audit log configuration | ${CORRO_EMAIL_REQUEST_RESPONSE_AUDITLOG} |

### Logging Configuration
```yaml
logging:
  level:
    root: ${CORRO_EMAIL_LOGGING_LEVEL_ROOT}
```
| Property | Description | Default |
|----------|-------------|---------|
| `logging.level.root` | Root logging level | ${CORRO_EMAIL_LOGGING_LEVEL_ROOT} |

### E-Notification Configuration
```yaml
eNotification:
  allowed:
    clients: SBGC,WELB,ARIC,ILNA,USAA
```
| Property | Description | Default |
|----------|-------------|---------|
| `eNotification.allowed.clients` | Allowed e-notification clients | SBGC,WELB,ARIC,ILNA,USAA |

### Document Type Configurations
```yaml
reminder:
  email:
    docType: DRRME

final:
  reminder:
    email:
      docType: FDRRME

agent:
  welcome:
    event: producer.ciamOnboarding.completed
    docType: AGTWCM

suitability:
  decline:
    docType: SUTDCL
```
| Property | Description | Default |
|----------|-------------|---------|
| `reminder.email.docType` | Reminder email document type | DRRME |
| `final.reminder.email.docType` | Final reminder email document type | FDRRME |
| `agent.welcome.event` | Agent welcome event | producer.ciamOnboarding.completed |
| `agent.welcome.docType` | Agent welcome document type | AGTWCM |
| `suitability.decline.docType` | Suitability decline document type | SUTDCL |

## Service Configuration

### Spring Configuration
```yaml
spring:
  application:
    name: nextgen-correspondence-service
  kafka:
    consumer:
      auto-offset-reset: earliest
```
| Property | Description | Default |
|----------|-------------|---------|
| `spring.application.name` | Service name | nextgen-correspondence-service |
| `spring.kafka.consumer.auto-offset-reset` | Kafka consumer offset reset policy | earliest |

### Rule Engine Configuration
```yaml
ruleengine:
  api:
    client: CORRO_WELLABE
    base:
      url: https://dev-bpm.se2.com
    execute:
      rule:
        url: ${ruleengine.api.base.url}/ruleengine/v1/businessrule/execute
    get:
      group:
        rules: ${ruleengine.api.base.url}/ruleengine/v1/group/externalId/{groupExternalId}/association/search
    authenticate:
      url: ${ruleengine.api.base.url}/uaam/authenticate
      username: ${RULEENGINE_API_AUTHENTICATE_USERNAME}
      password: ${RULEENGINE_API_AUTHENTICATE_PASSWORD}
```
| Property | Description | Default |
|----------|-------------|---------|
| `ruleengine.api.client` | Rule engine client | CORRO_WELLABE |
| `ruleengine.api.base.url` | Rule engine base URL | https://dev-bpm.se2.com |
| `ruleengine.api.execute.rule.url` | Rule execution URL | ${ruleengine.api.base.url}/ruleengine/v1/businessrule/execute |
| `ruleengine.api.get.group.rules` | Group rules URL | ${ruleengine.api.base.url}/ruleengine/v1/group/externalId/{groupExternalId}/association/search |
| `ruleengine.api.authenticate.url` | Authentication URL | ${ruleengine.api.base.url}/uaam/authenticate |
| `ruleengine.api.authenticate.username` | Authentication username | ${RULEENGINE_API_AUTHENTICATE_USERNAME} |
| `ruleengine.api.authenticate.password` | Authentication password | ${RULEENGINE_API_AUTHENTICATE_PASSWORD} |

### Cache Configuration
```yaml
app:
  cache:
    rule:
      refresh:
        interval: PT24H
    ruleresult:
      refresh:
        interval: PT24H
```
| Property | Description | Default |
|----------|-------------|---------|
| `app.cache.rule.refresh.interval` | Rule cache refresh interval | PT24H |
| `app.cache.ruleresult.refresh.interval` | Rule result cache refresh interval | PT24H |

### EDS Configuration
```yaml
eds:
  base:
    url: https://qa.api.zinnia.io/document/v3/documents
  api:
    search:
      url: ${eds.base.url}/search?limit=10&offset=0&x-correlation-id={correlationId}
      documentClassification: INBOUND
      documentType: POLPGFORM
    download:
      url: ${eds.base.url}/{documentId}/download
```
| Property | Description | Default |
|----------|-------------|---------|
| `eds.base.url` | EDS base URL | https://qa.api.zinnia.io/document/v3/documents |
| `eds.api.search.url` | Document search URL | ${eds.base.url}/search?limit=10&offset=0&x-correlation-id={correlationId} |
| `eds.api.search.documentClassification` | Document classification | INBOUND |
| `eds.api.search.documentType` | Document type | POLPGFORM |
| `eds.api.download.url` | Document download URL | ${eds.base.url}/{documentId}/download |

### Case Store Configuration
```yaml
caseStore:
  api:
    base:
      url: https://qa.api.zinnia.io/transactions/v1/transaction/search
```
| Property | Description | Default |
|----------|-------------|---------|
| `caseStore.api.base.url` | Case store API base URL | https://qa.api.zinnia.io/transactions/v1/transaction/search |

### Preferences Configuration
```yaml
preferences:
  api:
    base:
      url: https://qa.api.zinnia.io/preferences
  getepreferences:
    url: ${preferences.api.base.url}/v1/{partyId}/e-delivery/{planCode}/{policyNumber}?documentType={documentType}
  policyReceipt:
    docType: NWB
```
| Property | Description | Default |
|----------|-------------|---------|
| `preferences.api.base.url` | Preferences API base URL | https://qa.api.zinnia.io/preferences |
| `preferences.getepreferences.url` | E-preferences URL | ${preferences.api.base.url}/v1/{partyId}/e-delivery/{planCode}/{policyNumber}?documentType={documentType} |
| `preferences.policyReceipt.docType` | Policy receipt document type | NWB |

### Party Reference Configuration
```yaml
partyreference:
  api:
    base:
      url: https://qa.api.zinnia.io/party
  searchparties:
    url: ${partyreference.api.base.url}/v1/parties/reference/search?offset=0&limit=10
```
| Property | Description | Default |
|----------|-------------|---------|
| `partyreference.api.base.url` | Party reference API base URL | https://qa.api.zinnia.io/party |
| `partyreference.searchparties.url` | Party search URL | ${partyreference.api.base.url}/v1/parties/reference/search?offset=0&limit=10 |

### Zahara Configuration
```yaml
zahara:
  api:
    base:
      url: https://qa.api.zinnia.io/policy
  policyDetails:
    inputversion:
      url: ${zahara.api.base.url}/v1/policies/{planCode}/{policyNumber}?version={version}&viewDetails=true
    latestversion:
      url: ${zahara.api.base.url}/v1/policies/{planCode}/{policyNumber}?viewDetails=true
  transactionDetails:
    url: ${zahara.api.base.url}/v1/policies/{planCode}/{policyNumber}/transactions/{transactionId}
```
| Property | Description | Default |
|----------|-------------|---------|
| `zahara.api.base.url` | Zahara API base URL | https://qa.api.zinnia.io/policy |
| `zahara.policyDetails.inputversion.url` | Policy details input version URL | ${zahara.api.base.url}/v1/policies/{planCode}/{policyNumber}?version={version}&viewDetails=true |
| `zahara.policyDetails.latestversion.url` | Policy details latest version URL | ${zahara.api.base.url}/v1/policies/{planCode}/{policyNumber}?viewDetails=true |
| `zahara.transactionDetails.url` | Transaction details URL | ${zahara.api.base.url}/v1/policies/{planCode}/{policyNumber}/transactions/{transactionId} |

### Kafka Configuration
```yaml
kafka:
  configure: true
  key: ${CORRO_KAFKA_KEY}
  secret: ${CORRO_KAFKA_SECRET}
  bootstrapAddress: pkc-ymrq7.us-east-2.aws.confluent.cloud:9092
```
| Property | Description | Default |
|----------|-------------|---------|
| `kafka.configure` | Enable Kafka configuration | true |
| `kafka.key` | Kafka key | ${CORRO_KAFKA_KEY} |
| `kafka.secret` | Kafka secret | ${CORRO_KAFKA_SECRET} |
| `kafka.bootstrapAddress` | Kafka bootstrap address | pkc-ymrq7.us-east-2.aws.confluent.cloud:9092 |

### Kafka Topic Configurations
```yaml
zsor:
  kafkaEventAppTopic:
    topicName: ${ZSOR_KAFKA_EVENT_APP_TOPIC_NAME}
    groupId: ${ZSOR_KAFKA_EVENT_APP_TOPIC_GROUP_ID}
    configure: ${kafka.configure}
    bootstrapAddress: ${kafka.bootstrapAddress}
    key: ${kafka.key}
    secret: ${kafka.secret}

bpm:
  kafkaEventAppTopic:
    topicName: ${BPM_KAFKA_EVENT_APP_TOPIC_TOPIC_NAME}
    groupId: ${BPM_KAFKA_EVENT_APP_TOPIC_GROUP_ID}
    configure: ${kafka.configure}
    bootstrapAddress: ${kafka.bootstrapAddress}
    key: ${kafka.key}
    secret: ${kafka.secret}

dataprocessor:
  kafkaEventAppTopic:
    topicName: ${DATA_PROCESSOR_KAFKA_EVENT_APP_TOPIC_TOPIC_NAME}
    groupId: ${DATA_PROCESSOR_KAFKA_EVENT_APP_TOPIC_GROUP_ID}
    configure: ${kafka.configure}
    bootstrapAddress: ${kafka.bootstrapAddress}
    key: ${kafka.key}
    secret: ${kafka.secret}

cds:
  kafkaEventAppTopic:
    topicName: ${CDS_KAFKA_EVENT_APP_TOPIC_NAME}
    groupId: ${CDS_KAFKA_EVENT_APP_GROUP_ID}
    configure: ${kafka.configure}
    bootstrapAddress: ${kafka.bootstrapAddress}
    key: ${kafka.key}
    secret: ${kafka.secret}
```
| Property | Description | Default |
|----------|-------------|---------|
| `zsor.kafkaEventAppTopic.topicName` | ZSOR Kafka topic name | ${ZSOR_KAFKA_EVENT_APP_TOPIC_NAME} |
| `zsor.kafkaEventAppTopic.groupId` | ZSOR Kafka group ID | ${ZSOR_KAFKA_EVENT_APP_TOPIC_GROUP_ID} |
| `bpm.kafkaEventAppTopic.topicName` | BPM Kafka topic name | ${BPM_KAFKA_EVENT_APP_TOPIC_TOPIC_NAME} |
| `bpm.kafkaEventAppTopic.groupId` | BPM Kafka group ID | ${BPM_KAFKA_EVENT_APP_TOPIC_GROUP_ID} |
| `dataprocessor.kafkaEventAppTopic.topicName` | Data processor Kafka topic name | ${DATA_PROCESSOR_KAFKA_EVENT_APP_TOPIC_TOPIC_NAME} |
| `dataprocessor.kafkaEventAppTopic.groupId` | Data processor Kafka group ID | ${DATA_PROCESSOR_KAFKA_EVENT_APP_TOPIC_GROUP_ID} |
| `cds.kafkaEventAppTopic.topicName` | CDS Kafka topic name | ${CDS_KAFKA_EVENT_APP_TOPIC_NAME} |
| `cds.kafkaEventAppTopic.groupId` | CDS Kafka group ID | ${CDS_KAFKA_EVENT_APP_GROUP_ID} |

### Corro Configuration
```yaml
corro:
  new:
    business:
      admin:
        client: ADMIN_CLIENT_VALUE
  aws:
    accessKey: ${CORRO_AWS_ACCESS_KEY}
    secretKey: ${CORRO_AWS_SECRET_KEY}
    bucketName: strategiccorrespondencedev
    eds:
      folder: policypages/
    region: us-east-1
    originalRequest: WF_originalEvent
    dpRequest: WF_To_DP_Event
    cdsRequest: WF_To_CDS_Event
    policyData: WF_policyAPIResponse
    transactionData: WF_transactionDetailsAPIResponse
    previousVersionPolicyData: WF_previousVersionPolicyAPIResponse
    searchPartyRefData: WF_searchPartyRefAPIResponse
    eDeliveryPrefData: WF_getEDeliveryPrefAPIResponse
    caseStoreData: WF_caseStoreDataAPIResponse
    edsSearchDocs: WF_edsSearchDocumentsDataAPIResponse
  cloudberryPath: \\\\cloudberryshare.zinnia.com/
  dataProcessor:
    consumerReadTimeout: 180000
```
| Property | Description | Default |
|----------|-------------|---------|
| `corro.new.business.admin.client` | Admin client value | ADMIN_CLIENT_VALUE |
| `corro.aws.accessKey` | AWS access key | ${CORRO_AWS_ACCESS_KEY} |
| `corro.aws.secretKey` | AWS secret key | ${CORRO_AWS_SECRET_KEY} |
| `corro.aws.bucketName` | AWS bucket name | strategiccorrespondencedev |
| `corro.aws.eds.folder` | EDS folder path | policypages/ |
| `corro.aws.region` | AWS region | us-east-1 |
| `corro.aws.originalRequest` | Original request workflow | WF_originalEvent |
| `corro.aws.dpRequest` | Data processor request workflow | WF_To_DP_Event |
| `corro.aws.cdsRequest` | CDS request workflow | WF_To_CDS_Event |
| `corro.aws.policyData` | Policy data workflow | WF_policyAPIResponse |
| `corro.aws.transactionData` | Transaction data workflow | WF_transactionDetailsAPIResponse |
| `corro.aws.previousVersionPolicyData` | Previous version policy data workflow | WF_previousVersionPolicyAPIResponse |
| `corro.aws.searchPartyRefData` | Party reference data workflow | WF_searchPartyRefAPIResponse |
| `corro.aws.eDeliveryPrefData` | E-delivery preferences workflow | WF_getEDeliveryPrefAPIResponse |
| `corro.aws.caseStoreData` | Case store data workflow | WF_caseStoreDataAPIResponse |
| `corro.aws.edsSearchDocs` | EDS search documents workflow | WF_edsSearchDocumentsDataAPIResponse |
| `corro.cloudberryPath` | Cloudberry share path | \\\\cloudberryshare.zinnia.com/ |
| `corro.dataProcessor.consumerReadTimeout` | Data processor consumer read timeout | 180000 |

### Auth Configuration
```yaml
auth:
  apiUrl: https://login.qa.zinnia.com/oauth/token
  audience: https://qa.api.zinnia.io
  grantType: client_credentials
  clientId: ${AUTH_CLIENT_ID}
  clientSecret: ${AUTH_CLIENT_SECRET}
```
| Property | Description | Default |
|----------|-------------|---------|
| `auth.apiUrl` | Authentication API URL | https://login.qa.zinnia.com/oauth/token |
| `auth.audience` | Authentication audience | https://qa.api.zinnia.io |
| `auth.grantType` | Authentication grant type | client_credentials |
| `auth.clientId` | Authentication client ID | ${AUTH_CLIENT_ID} |
| `auth.clientSecret` | Authentication client secret | ${AUTH_CLIENT_SECRET} |

### Cache Configuration
```yaml
cache:
  ttl: 10
  maxSize: 1000
```
| Property | Description | Default |
|----------|-------------|---------|
| `cache.ttl` | Cache time-to-live in minutes | 10 |
| `cache.maxSize` | Maximum cache size | 1000 |

### Environment Configuration
```yaml
env: dev
```
| Property | Description | Default |
|----------|-------------|---------|
| `env` | Environment name | dev |

## Scheduler Configuration

The scheduler configuration is available for DEV, QA, UAT, and PROD environments.

### MongoDB Configuration
```yaml
mongodb:
  user: correspondence-{env}-user
  database: nextgen_correspondence
```
| Property | Description | Default |
|----------|-------------|---------|
| `mongodb.user` | MongoDB user | correspondence-{env}-user |
| `mongodb.database` | MongoDB database | nextgen_correspondence |

### Kafka Configuration
```yaml
kafka:
  bootstrap:
    servers: pkc-ymrq7.us-east-2.aws.confluent.cloud:9092
  security:
    protocol: PLAINTEXT
  sasl:
    mechanisms: PLAIN
```
| Property | Description | Default |
|----------|-------------|---------|
| `kafka.bootstrap.servers` | Kafka bootstrap servers | pkc-ymrq7.us-east-2.aws.confluent.cloud:9092 |
| `kafka.security.protocol` | Kafka security protocol | PLAINTEXT |
| `kafka.sasl.mechanisms` | Kafka SASL mechanisms | PLAIN |

### Timed Correspondence Configuration
```yaml
timed:
  corro:
    topic: zinnia.correspondence.timed-corro
```
| Property | Description | Default |
|----------|-------------|---------|
| `timed.corro.topic` | Timed correspondence Kafka topic | zinnia.correspondence.timed-corro |

### Rule Engine Configuration
```yaml
ruleengine:
  api:
    client:
      code: CORRO
```
| Property | Description | Default |
|----------|-------------|---------|
| `ruleengine.api.client.code` | Rule engine client code | CORRO |

### API Configuration
```yaml
api:
  max:
    retry: 3
  initial:
    retry:
      ms: 1000
  retry:
    multiplier: 2.0
```
| Property | Description | Default |
|----------|-------------|---------|
| `api.max.retry` | Maximum number of API retries | 3 |
| `api.initial.retry.ms` | Initial retry delay in milliseconds | 1000 |
| `api.retry.multiplier` | Retry delay multiplier | 2.0 |

### Datadog Configuration
```yaml
datadog:
  enabled: true
  env: {env}
```
| Property | Description | Default |
|----------|-------------|---------|
| `datadog.enabled` | Enable Datadog monitoring | true |
| `datadog.env` | Datadog environment | {env} |

## Environment Variables

The following environment variables are used across configurations:

### Email Service
- `CORRO_EMAIL_MONGO_USER`: MongoDB username
- `CORRO_EMAIL_MONGO_PASSWORD`: MongoDB password
- `CORRO_EMAIL_KAFKA_CONSUMER_KEY`: Kafka consumer key
- `CORRO_EMAIL_KAFKA_CONSUMER_SECRET`: Kafka consumer secret
- `CORRO_EMAIL_CIAM_CLIENT_ID`: CIAM client ID
- `CORRO_EMAIL_CIAM_CLIENT_SECRET`: CIAM client secret
- `CORRO_EMAIL_BPM_RULE_ENGINE_USERNAME`: Rule engine username
- `CORRO_EMAIL_BPM_RULE_ENGINE_PASSWORD`: Rule engine password
- `CORRO_EMAIL_REQUEST_RESPONSE_AUDITLOG`: Audit log configuration
- `CORRO_EMAIL_LOGGING_LEVEL_ROOT`: Root logging level

### Service
- `RULEENGINE_API_AUTHENTICATE_USERNAME`: Rule engine authentication username
- `RULEENGINE_API_AUTHENTICATE_PASSWORD`: Rule engine authentication password
- `CORRO_KAFKA_KEY`: Kafka key
- `CORRO_KAFKA_SECRET`: Kafka secret
- `CORRO_AWS_ACCESS_KEY`: AWS access key
- `CORRO_AWS_SECRET_KEY`: AWS secret key
- `AUTH_CLIENT_ID`: Authentication client ID
- `AUTH_CLIENT_SECRET`: Authentication client secret

### Kafka Topics
- `ZSOR_KAFKA_EVENT_APP_TOPIC_NAME`: ZSOR Kafka topic name
- `ZSOR_KAFKA_EVENT_APP_TOPIC_GROUP_ID`: ZSOR Kafka group ID
- `BPM_KAFKA_EVENT_APP_TOPIC_TOPIC_NAME`: BPM Kafka topic name
- `BPM_KAFKA_EVENT_APP_TOPIC_GROUP_ID`: BPM Kafka group ID
- `DATA_PROCESSOR_KAFKA_EVENT_APP_TOPIC_TOPIC_NAME`: Data processor Kafka topic name
- `DATA_PROCESSOR_KAFKA_EVENT_APP_TOPIC_GROUP_ID`: Data processor Kafka group ID
- `CDS_KAFKA_EVENT_APP_TOPIC_NAME`: CDS Kafka topic name
- `CDS_KAFKA_EVENT_APP_GROUP_ID`: CDS Kafka group ID
